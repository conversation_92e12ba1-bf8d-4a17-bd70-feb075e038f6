"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Github, Home, Mail } from "lucide-react"
// Import the Footer component
import { Footer } from "@/components/footer"

export default function LoginPage() {
  const [activeTab, setActiveTab] = useState("login")

  return (
    <div className="flex min-h-screen bg-[#1e2a36] text-white">
      <div className="flex-1 flex flex-col">
        <header className="sticky top-0 z-10 bg-[#1e2a36]/80 backdrop-blur-md border-b border-[#2a3a4a] p-4 flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-2">
            <ArrowLeft className="h-5 w-5 text-[#F2C1AE]/70" />
            <Image src="/images/genvibe-logo.png" alt="GenVibe Logo" width={100} height={24} className="h-7 w-auto" />
          </Link>
          <Link href="/">
            <Button variant="ghost" className="text-[#F2C1AE]/70 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]/50">
              <Home className="h-5 w-5" />
            </Button>
          </Link>
        </header>

        <main className="flex items-center justify-center flex-1 p-4">
          <Card className="w-full max-w-md bg-[#2a3a4a]/60 border-[#2a3a4a]">
            <CardHeader>
              <CardTitle className="text-xl bg-gradient-to-r from-[#F24F13] to-[#F26430] bg-clip-text text-transparent">
                Welcome to GenVibe
              </CardTitle>
              <CardDescription className="text-[#F2C1AE]/70">
                Sign in to your account or create a new one
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="login" onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-2 bg-[#1e2a36]/50">
                  <TabsTrigger
                    value="login"
                    className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#F24F13] data-[state=active]:to-[#F26430] data-[state=active]:text-white"
                  >
                    Login
                  </TabsTrigger>
                  <TabsTrigger
                    value="register"
                    className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#F24F13] data-[state=active]:to-[#F26430] data-[state=active]:text-white"
                  >
                    Register
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="login" className="mt-6">
                  <div className="space-y-4">
                    <Button
                      variant="outline"
                      className="w-full border-[#2a3a4a] text-[#F2C1AE]/70 hover:bg-[#2a3a4a] hover:text-[#F2C1AE]"
                    >
                      <Github className="h-4 w-4 mr-2" />
                      Continue with GitHub
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full border-[#2a3a4a] text-[#F2C1AE]/70 hover:bg-[#2a3a4a] hover:text-[#F2C1AE]"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Continue with Google
                    </Button>

                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-[#2a3a4a]" />
                      </div>
                      <div className="relative flex justify-center text-xs">
                        <span className="bg-[#2a3a4a] px-2 text-[#F2C1AE]/50">OR CONTINUE WITH</span>
                      </div>
                    </div>

                    <form className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-[#F2C1AE]/70">
                          Email
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430]"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="password" className="text-[#F2C1AE]/70">
                            Password
                          </Label>
                          <Link href="#" className="text-sm text-[#F24F13] hover:underline">
                            Forgot password?
                          </Link>
                        </div>
                        <Input
                          id="password"
                          type="password"
                          className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430]"
                          required
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="remember"
                          className="border-[#2a3a4a] data-[state=checked]:bg-[#F24F13] data-[state=checked]:border-[#F24F13]"
                        />
                        <label
                          htmlFor="remember"
                          className="text-sm text-[#F2C1AE]/70 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Remember me
                        </label>
                      </div>
                      <Button
                        type="submit"
                        className="w-full bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white"
                      >
                        Login
                      </Button>
                    </form>
                  </div>
                </TabsContent>
                <TabsContent value="register" className="mt-6">
                  <div className="space-y-4">
                    <Button
                      variant="outline"
                      className="w-full border-[#2a3a4a] text-[#F2C1AE]/70 hover:bg-[#2a3a4a] hover:text-[#F2C1AE]"
                    >
                      <Github className="h-4 w-4 mr-2" />
                      Sign up with GitHub
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full border-[#2a3a4a] text-[#F2C1AE]/70 hover:bg-[#2a3a4a] hover:text-[#F2C1AE]"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Sign up with Google
                    </Button>

                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-[#2a3a4a]" />
                      </div>
                      <div className="relative flex justify-center text-xs">
                        <span className="bg-[#2a3a4a] px-2 text-[#F2C1AE]/50">OR CONTINUE WITH</span>
                      </div>
                    </div>

                    <form className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="register-username" className="text-[#F2C1AE]/70">
                          Username
                        </Label>
                        <Input
                          id="register-username"
                          placeholder="johndoe"
                          className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430]"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="register-email" className="text-[#F2C1AE]/70">
                          Email
                        </Label>
                        <Input
                          id="register-email"
                          type="email"
                          placeholder="<EMAIL>"
                          className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430]"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="register-password" className="text-[#F2C1AE]/70">
                          Password
                        </Label>
                        <Input
                          id="register-password"
                          type="password"
                          className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430]"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="register-confirm" className="text-[#F2C1AE]/70">
                          Confirm Password
                        </Label>
                        <Input
                          id="register-confirm"
                          type="password"
                          className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430]"
                          required
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="terms"
                          className="border-[#2a3a4a] data-[state=checked]:bg-[#F24F13] data-[state=checked]:border-[#F24F13]"
                        />
                        <label
                          htmlFor="terms"
                          className="text-sm text-[#F2C1AE]/70 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          I agree to the{" "}
                          <Link href="#" className="text-[#F24F13] hover:underline">
                            Terms of Service
                          </Link>{" "}
                          and{" "}
                          <Link href="#" className="text-[#F24F13] hover:underline">
                            Privacy Policy
                          </Link>
                        </label>
                      </div>
                      <Button
                        type="submit"
                        className="w-full bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white"
                      >
                        Register
                      </Button>
                    </form>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter className="flex flex-col border-t border-[#2a3a4a] pt-4">
              <div className="text-sm text-[#F2C1AE]/70 text-center">
                {activeTab === "login" ? (
                  <p>
                    Don't have an account?{" "}
                    <button className="text-[#F24F13] hover:underline" onClick={() => setActiveTab("register")}>
                      Sign up
                    </button>
                  </p>
                ) : (
                  <p>
                    Already have an account?{" "}
                    <button className="text-[#F24F13] hover:underline" onClick={() => setActiveTab("login")}>
                      Sign in
                    </button>
                  </p>
                )}
              </div>
            </CardFooter>
          </Card>
        </main>
        <Footer />
      </div>
    </div>
  )
}
