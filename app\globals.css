@tailwind base;
@tailwind components;
@tailwind utilities;

/* Improve color accessibility */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 15 89% 51%;
    --primary-foreground: 0 0% 98%;
    --secondary: 14 89% 57%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 14 79% 66%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 15 89% 51%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 215 28% 17%;
    --foreground: 0 0% 98%;
    --card: 215 28% 17%;
    --card-foreground: 0 0% 98%;
    --popover: 215 28% 17%;
    --popover-foreground: 0 0% 98%;
    --primary: 15 89% 51%;
    --primary-foreground: 0 0% 98%;
    --secondary: 14 89% 57%;
    --secondary-foreground: 0 0% 98%;
    --muted: 215 28% 25%;
    --muted-foreground: 240 5% 84.9%;
    --accent: 14 79% 66%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 215 28% 25%;
    --input: 215 28% 25%;
    --ring: 15 89% 51%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
