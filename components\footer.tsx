import Link from "next/link"

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-[#1e2a36] border-t border-[#2a3a4a] py-6 px-4 mt-8">
      <div className="max-w-5xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <p className="text-[#F2C1AE]/70 text-sm">© {currentYear} All rights reserved by GenVibe</p>
          </div>
          <div className="flex space-x-6">
            <Link href="/about" className="text-[#F2C1AE]/70 hover:text-[#F2C1AE] text-sm transition-colors">
              About
            </Link>
            <Link href="/privacy" className="text-[#F2C1AE]/70 hover:text-[#F2C1AE] text-sm transition-colors">
              Privacy
            </Link>
            <Link href="/terms" className="text-[#F2C1AE]/70 hover:text-[#F2C1AE] text-sm transition-colors">
              Terms
            </Link>
            <Link href="/contact" className="text-[#F2C1AE]/70 hover:text-[#F2C1AE] text-sm transition-colors">
              Contact
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
