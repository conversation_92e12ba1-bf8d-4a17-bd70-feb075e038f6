"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  Bell,
  Bookmark,
  Compass,
  Home,
  LogIn,
  MessageSquare,
  MoreHorizontal,
  Plus,
  Search,
  Share,
  TrendingUp,
} from "lucide-react"

// Import the Footer component
import { Footer } from "@/components/footer"

export default function ThreadPage({ params }: { params: { id: string } }) {
  const [upvotes, setUpvotes] = useState({
    thread: 42,
    replies: {
      "reply-1": 15,
      "reply-2": 8,
      "reply-3": 3,
    },
  })

  const handleUpvote = (id: string) => {
    if (id === "thread") {
      setUpvotes((prev) => ({
        ...prev,
        thread: prev.thread + 1,
      }))
    } else {
      setUpvotes((prev) => ({
        ...prev,
        replies: {
          ...prev.replies,
          [id]: (prev.replies[id] || 0) + 1,
        },
      }))
    }
  }

  const handleDownvote = (id: string) => {
    if (id === "thread") {
      setUpvotes((prev) => ({
        ...prev,
        thread: prev.thread - 1,
      }))
    } else {
      setUpvotes((prev) => ({
        ...prev,
        replies: {
          ...prev.replies,
          [id]: Math.max(0, (prev.replies[id] || 0) - 1),
        },
      }))
    }
  }

  // Mock thread data
  const thread = {
    id: params.id,
    title: "Welcome to GenVibe!",
    category: "announcements",
    categoryName: "Announcements",
    author: "Sarah",
    authorAvatar: "/placeholder.svg?height=40&width=40",
    date: "2 days ago",
    content:
      "Hello everyone! Welcome to our community forum. This is a place where we can discuss various topics, share ideas, and help each other out. Please make sure to read the community guidelines before posting. Let's keep this a friendly and productive space for everyone. Feel free to introduce yourself in the introductions category. Looking forward to engaging with all of you!",
  }

  // Mock replies data
  const replies = [
    {
      id: "reply-1",
      author: "Alex",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "1 day ago",
      content:
        "Thanks for the welcome! I'm excited to be part of this community and looking forward to the discussions.",
    },
    {
      id: "reply-2",
      author: "Michael",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "1 day ago",
      content:
        "Great to be here! I have a lot of questions about Next.js and Vercel, so this forum will be very helpful.",
    },
    {
      id: "reply-3",
      author: "Jessica",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "12 hours ago",
      content:
        "Hello everyone! Just joined and already finding a lot of useful information here. Thanks for creating this space.",
    },
  ]

  // Mock data for trending topics
  const trendingTopics = ["Next.js", "React", "Tailwind CSS", "TypeScript", "Vercel"]

  // Get category color based on category
  const getCategoryColor = (category: string) => {
    switch (category) {
      case "announcements":
        return "bg-[#F24F13]/20 text-[#F24F13] hover:bg-[#F24F13]/30"
      case "introductions":
        return "bg-[#F2865E]/20 text-[#F2865E] hover:bg-[#F2865E]/30"
      default:
        return "bg-[#F26430]/20 text-[#F26430] hover:bg-[#F26430]/30"
    }
  }

  return (
    <div className="flex min-h-screen bg-[#1e2a36] text-white">
      {/* Sidebar */}
      <div className="hidden md:flex w-64 flex-col bg-[#1e2a36]/80 backdrop-blur-sm border-r border-[#2a3a4a]">
        <div className="p-4 border-b border-[#2a3a4a] flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-2">
            <Image src="/images/genvibe-logo.png" alt="GenVibe Logo" width={120} height={30} className="h-8 w-auto" />
          </Link>
        </div>

        {/* Trending topics at the top */}
        <div className="p-4 border-b border-[#2a3a4a]">
          <h3 className="text-xs font-medium uppercase text-[#F2C1AE] tracking-wider mb-2">Trending</h3>
          <div className="flex flex-wrap gap-2">
            {trendingTopics.map((topic) => (
              <Link
                key={topic}
                href={`/topic/${topic.toLowerCase().replace(/\s+/g, "-")}`}
                className="text-xs px-2 py-1 rounded-full bg-[#2a3a4a] text-[#F2C1AE] hover:bg-[#F24F13]/20 hover:text-[#F2C1AE] transition-colors"
              >
                {topic}
              </Link>
            ))}
          </div>
        </div>

        <div className="p-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-[#F2C1AE]/50" />
            <Input
              type="search"
              placeholder="Search..."
              className="w-full bg-[#2a3a4a]/80 border-[#2a3a4a] pl-9 text-sm text-[#F2C1AE] placeholder:text-[#F2C1AE]/50 focus:border-[#F26430]"
            />
          </div>
        </div>

        <nav className="flex-1 p-4 space-y-1">
          <Link
            href="/"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <Home className="h-4 w-4" />
            <span>Home</span>
          </Link>
          <Link
            href="/popular"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <TrendingUp className="h-4 w-4" />
            <span>Popular</span>
          </Link>
          <Link
            href="/explore"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <Compass className="h-4 w-4" />
            <span>Explore</span>
          </Link>
          <Link
            href="/bookmarks"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <Bookmark className="h-4 w-4" />
            <span>Bookmarks</span>
          </Link>
          <Link
            href="/notifications"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <Bell className="h-4 w-4" />
            <span>Notifications</span>
            <Badge className="ml-auto bg-[#F24F13] text-white text-xs">3</Badge>
          </Link>
        </nav>

        <div className="p-4 mt-auto border-t border-[#2a3a4a]">
          <Button className="w-full bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white">
            <Plus className="h-4 w-4 mr-2" />
            New Post
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-auto">
        <header className="sticky top-0 z-10 bg-[#1e2a36]/80 backdrop-blur-md border-b border-[#2a3a4a] p-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Link
              href={`/category/${thread.category}`}
              className="text-[#F2C1AE]/80 hover:text-[#F2C1AE] transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span className="sr-only">Back</span>
            </Link>
            <h2 className="text-lg font-medium truncate">{thread.title}</h2>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a] sm:flex hidden"
            >
              <Share className="h-4 w-4 mr-2" />
              <span>Share</span>
            </Button>
            <Link href="/login">
              <Button
                variant="outline"
                className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
              >
                <LogIn className="h-4 w-4" />
                <span className="sr-only sm:not-sr-only sm:ml-2">Login</span>
              </Button>
            </Link>
          </div>
        </header>

        <main className="p-4 md:p-6 max-w-5xl mx-auto">
          {/* Mobile trending topics */}
          <div className="md:hidden mb-6 overflow-x-auto pb-2">
            <div className="flex gap-2 w-max">
              {trendingTopics.map((topic) => (
                <Link
                  key={topic}
                  href={`/topic/${topic.toLowerCase().replace(/\s+/g, "-")}`}
                  className="text-xs px-2 py-1 rounded-full bg-[#2a3a4a] text-[#F2C1AE] hover:bg-[#F24F13]/20 hover:text-[#F2C1AE] transition-colors whitespace-nowrap"
                >
                  {topic}
                </Link>
              ))}
            </div>
          </div>

          {/* Thread */}
          <Card className="mb-6 bg-[#2a3a4a]/60 border-[#2a3a4a] overflow-hidden">
            <div className="flex">
              {/* Voting */}
              <div className="flex flex-col items-center p-4 border-r border-[#2a3a4a] text-[#F2C1AE]/70">
                <button className="hover:text-[#F24F13] transition-colors" onClick={() => handleUpvote("thread")}>
                  <ArrowUp className="h-5 w-5" />
                </button>
                <span className="my-1 font-medium">{upvotes.thread}</span>
                <button className="hover:text-[#F26430] transition-colors" onClick={() => handleDownvote("thread")}>
                  <ArrowDown className="h-5 w-5" />
                </button>
              </div>

              {/* Content */}
              <div className="flex-1">
                <CardHeader className="pb-2">
                  <div className="flex flex-wrap items-center gap-2 mb-2">
                    <Badge className={getCategoryColor(thread.category)}>{thread.categoryName}</Badge>
                  </div>

                  <h1 className="text-xl font-medium">{thread.title}</h1>
                  <div className="flex items-center gap-2 mt-2 text-sm text-[#F2C1AE]/70">
                    <Avatar className="h-5 w-5 border border-[#2a3a4a]">
                      <AvatarImage src={thread.authorAvatar} alt={thread.author} />
                      <AvatarFallback className="text-xs bg-[#F26430] text-white">
                        {thread.author[0].toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span>{thread.author}</span>
                    <span>•</span>
                    <span>{thread.date}</span>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-[#F2C1AE] leading-relaxed">{thread.content}</p>

                  <div className="flex items-center justify-between mt-6 pt-4 border-t border-[#2a3a4a]">
                    <div className="flex items-center gap-4 text-sm text-[#F2C1AE]/70">
                      <div className="flex items-center">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        <span>{replies.length} comments</span>
                      </div>
                      <button className="flex items-center hover:text-[#F2C1AE] transition-colors">
                        <Share className="h-4 w-4 mr-1" />
                        <span>Share</span>
                      </button>
                    </div>
                    <button className="text-[#F2C1AE]/70 hover:text-[#F2C1AE] transition-colors">
                      <MoreHorizontal className="h-5 w-5" />
                    </button>
                  </div>
                </CardContent>
              </div>
            </div>
          </Card>

          {/* Comment form */}
          <Card className="mb-8 bg-[#2a3a4a]/60 border-[#2a3a4a]">
            <CardHeader className="pb-2">
              <h3 className="text-sm font-medium text-[#F2C1AE]/70">
                Comment as <span className="text-[#F24F13]">guest</span>
              </h3>
            </CardHeader>
            <CardContent className="pb-3">
              <Textarea
                placeholder="What are your thoughts?"
                className="min-h-[100px] bg-[#2a3a4a]/80 border-[#2a3a4a] focus:border-[#F26430] text-white resize-none"
              />
            </CardContent>
            <CardFooter className="border-t border-[#2a3a4a] py-3 flex justify-end">
              <Button className="bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white">
                Comment
              </Button>
            </CardFooter>
          </Card>

          {/* Comments */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium">Comments ({replies.length})</h2>
              <div className="flex items-center gap-2 text-sm">
                <span className="text-[#F2C1AE]/70">Sort by:</span>
                <button className="text-[#F24F13] hover:underline">Best</button>
              </div>
            </div>

            {replies.map((reply) => (
              <Card key={reply.id} className="bg-[#2a3a4a]/60 border-[#2a3a4a] overflow-hidden">
                <div className="flex">
                  {/* Voting */}
                  <div className="flex flex-col items-center p-4 border-r border-[#2a3a4a] text-[#F2C1AE]/70">
                    <button className="hover:text-[#F24F13] transition-colors" onClick={() => handleUpvote(reply.id)}>
                      <ArrowUp className="h-5 w-5" />
                    </button>
                    <span className="my-1 font-medium">{upvotes.replies[reply.id] || 0}</span>
                    <button className="hover:text-[#F26430] transition-colors" onClick={() => handleDownvote(reply.id)}>
                      <ArrowDown className="h-5 w-5" />
                    </button>
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2 text-sm text-[#F2C1AE]/70">
                        <Avatar className="h-5 w-5 border border-[#2a3a4a]">
                          <AvatarImage src={reply.authorAvatar} alt={reply.author} />
                          <AvatarFallback className="text-xs bg-[#F26430] text-white">
                            {reply.author[0].toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium text-[#F2C1AE]">{reply.author}</span>
                        <span>•</span>
                        <span>{reply.date}</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-[#F2C1AE]">{reply.content}</p>

                      <div className="flex items-center gap-4 mt-4 text-sm text-[#F2C1AE]/50">
                        <button className="hover:text-[#F2C1AE] transition-colors">Reply</button>
                        <button className="hover:text-[#F2C1AE] transition-colors">Share</button>
                        <button className="hover:text-[#F2C1AE] transition-colors">Report</button>
                      </div>
                    </CardContent>
                  </div>
                </div>
              </Card>
            ))}

            <div className="flex justify-center mt-6">
              <Button
                variant="outline"
                className="border-[#2a3a4a] text-[#F2C1AE]/70 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
              >
                Load More Comments
              </Button>
            </div>
          </div>
        </main>

        {/* Mobile new post button */}
        <div className="md:hidden fixed bottom-4 right-4">
          <Button className="h-12 w-12 rounded-full bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white shadow-lg">
            <Plus className="h-6 w-6" />
          </Button>
        </div>
        <Footer />
      </div>
    </div>
  )
}
