"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  Bell,
  Bookmark,
  Compass,
  Filter,
  Home,
  LogIn,
  MessageSquare,
  Plus,
  Search,
  TrendingUp,
} from "lucide-react"

// Import the Footer component
import { Footer } from "@/components/footer"

export default function CategoryPage({ params }: { params: { slug: string } }) {
  const [upvotes, setUpvotes] = useState<Record<string, number>>({
    "thread-1": 42,
    "thread-2": 18,
    "thread-3": 31,
    "thread-4": 76,
    "thread-5": 53,
  })

  const handleUpvote = (id: string) => {
    setUpvotes((prev) => ({
      ...prev,
      [id]: (prev[id] || 0) + 1,
    }))
  }

  const handleDownvote = (id: string) => {
    setUpvotes((prev) => ({
      ...prev,
      [id]: Math.max(0, (prev[id] || 0) - 1),
    }))
  }

  // Mock data for threads
  const threads = [
    {
      id: "thread-1",
      title: "Welcome to GenVibe!",
      author: "Sarah",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "2 days ago",
      replies: 24,
      pinned: true,
      excerpt: "This is a welcome thread for all new members. Please read the rules before posting.",
    },
    {
      id: "thread-2",
      title: "How do I get started with Next.js?",
      author: "Alex",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "1 day ago",
      replies: 15,
      pinned: false,
      excerpt: "I'm new to Next.js and would like some guidance on how to get started with my first project.",
    },
    {
      id: "thread-3",
      title: "Sharing my experience with Vercel deployment",
      author: "Michael",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "12 hours ago",
      replies: 8,
      pinned: false,
      excerpt: "I recently deployed my application to Vercel and wanted to share my experience with the community.",
    },
    {
      id: "thread-4",
      title: "Best practices for state management in React applications",
      author: "Emma",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "3 days ago",
      replies: 42,
      pinned: false,
      excerpt:
        "I've been working with React for several years and wanted to share some best practices for state management that I've learned along the way.",
    },
    {
      id: "thread-5",
      title: "How to optimize your Next.js application for production",
      author: "Daniel",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "5 days ago",
      replies: 19,
      pinned: false,
      excerpt:
        "In this thread, I'll share some tips and tricks for optimizing your Next.js application for production deployment.",
    },
  ]

  // Get category name from slug
  const categoryNames: Record<string, string> = {
    general: "General Discussion",
    introductions: "Introductions",
    announcements: "Announcements",
  }

  const categoryName = categoryNames[params.slug] || params.slug

  // Get category color based on slug
  const getCategoryColor = (slug: string) => {
    switch (slug) {
      case "announcements":
        return "bg-[#F24F13]/20 text-[#F24F13]"
      case "introductions":
        return "bg-[#F2865E]/20 text-[#F2865E]"
      default:
        return "bg-[#F26430]/20 text-[#F26430]"
    }
  }

  // Mock data for trending topics
  const trendingTopics = ["Next.js", "React", "Tailwind CSS", "TypeScript", "Vercel"]

  return (
    <div className="flex min-h-screen bg-[#1e2a36] text-white">
      {/* Sidebar */}
      <div className="hidden md:flex w-64 flex-col bg-[#1e2a36]/80 backdrop-blur-sm border-r border-[#2a3a4a]">
        <div className="p-4 border-b border-[#2a3a4a] flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-2">
            <Image src="/images/genvibe-logo.png" alt="GenVibe Logo" width={120} height={30} className="h-8 w-auto" />
          </Link>
        </div>

        {/* Trending topics at the top */}
        <div className="p-4 border-b border-[#2a3a4a]">
          <h3 className="text-xs font-medium uppercase text-[#F2C1AE] tracking-wider mb-2">Trending</h3>
          <div className="flex flex-wrap gap-2">
            {trendingTopics.map((topic) => (
              <Link
                key={topic}
                href={`/topic/${topic.toLowerCase().replace(/\s+/g, "-")}`}
                className="text-xs px-2 py-1 rounded-full bg-[#2a3a4a] text-[#F2C1AE] hover:bg-[#F24F13]/20 hover:text-[#F2C1AE] transition-colors"
              >
                {topic}
              </Link>
            ))}
          </div>
        </div>

        <div className="p-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-[#F2C1AE]/50" />
            <Input
              type="search"
              placeholder="Search..."
              className="w-full bg-[#2a3a4a]/80 border-[#2a3a4a] pl-9 text-sm text-[#F2C1AE] placeholder:text-[#F2C1AE]/50 focus:border-[#F26430]"
            />
          </div>
        </div>

        <nav className="flex-1 p-4 space-y-1">
          <Link
            href="/"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <Home className="h-4 w-4" />
            <span>Home</span>
          </Link>
          <Link
            href="/popular"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <TrendingUp className="h-4 w-4" />
            <span>Popular</span>
          </Link>
          <Link
            href="/explore"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <Compass className="h-4 w-4" />
            <span>Explore</span>
          </Link>
          <Link
            href="/bookmarks"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <Bookmark className="h-4 w-4" />
            <span>Bookmarks</span>
          </Link>
          <Link
            href="/notifications"
            className="flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors"
          >
            <Bell className="h-4 w-4" />
            <span>Notifications</span>
            <Badge className="ml-auto bg-[#F24F13] text-white text-xs">3</Badge>
          </Link>
        </nav>

        <div className="p-4 mt-auto border-t border-[#2a3a4a]">
          <Button className="w-full bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white">
            <Plus className="h-4 w-4 mr-2" />
            New Post
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-auto">
        <header className="sticky top-0 z-10 bg-[#1e2a36]/80 backdrop-blur-md border-b border-[#2a3a4a] p-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Link href="/" className="text-[#F2C1AE]/80 hover:text-[#F2C1AE] transition-colors">
              <ArrowLeft className="h-5 w-5" />
              <span className="sr-only">Back</span>
            </Link>
            <div className={`px-2 py-1 rounded ${getCategoryColor(params.slug)}`}>
              <h2 className="text-lg font-medium">{categoryName}</h2>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a] sm:flex hidden"
            >
              <Filter className="h-4 w-4 mr-2" />
              <span>Filter</span>
            </Button>
            <Link href="/login">
              <Button
                variant="outline"
                className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
              >
                <LogIn className="h-4 w-4" />
                <span className="sr-only sm:not-sr-only sm:ml-2">Login</span>
              </Button>
            </Link>
          </div>
        </header>

        <main className="p-4 md:p-6 max-w-5xl mx-auto">
          {/* Mobile trending topics */}
          <div className="md:hidden mb-6 overflow-x-auto pb-2">
            <div className="flex gap-2 w-max">
              {trendingTopics.map((topic) => (
                <Link
                  key={topic}
                  href={`/topic/${topic.toLowerCase().replace(/\s+/g, "-")}`}
                  className="text-xs px-2 py-1 rounded-full bg-[#2a3a4a] text-[#F2C1AE] hover:bg-[#F24F13]/20 hover:text-[#F2C1AE] transition-colors whitespace-nowrap"
                >
                  {topic}
                </Link>
              ))}
            </div>
          </div>

          <div className="mb-6">
            <div className="relative max-w-md w-full mb-6">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-[#F2C1AE]/50" />
              <Input
                type="search"
                placeholder={`Search in ${categoryName.toLowerCase()}...`}
                className="w-full bg-[#2a3a4a]/80 border-[#2a3a4a] pl-9 text-sm text-[#F2C1AE] placeholder:text-[#F2C1AE]/50 focus:border-[#F26430]"
              />
            </div>

            <div className="flex flex-wrap gap-2 mb-2">
              <Badge className="bg-[#F24F13] hover:bg-[#F26430] text-white">Latest</Badge>
              <Badge
                variant="outline"
                className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:bg-[#2a3a4a] hover:text-[#F2C1AE]"
              >
                Top
              </Badge>
              <Badge
                variant="outline"
                className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:bg-[#2a3a4a] hover:text-[#F2C1AE]"
              >
                Hot
              </Badge>
            </div>
          </div>

          <div className="grid gap-4">
            {threads.map((thread) => (
              <Card
                key={thread.id}
                className="bg-[#2a3a4a]/60 border-[#2a3a4a] hover:border-[#F26430]/50 transition-colors overflow-hidden"
              >
                <div className="flex">
                  {/* Voting */}
                  <div className="flex flex-col items-center p-4 border-r border-[#2a3a4a] text-[#F2C1AE]/70">
                    <button className="hover:text-[#F24F13] transition-colors" onClick={() => handleUpvote(thread.id)}>
                      <ArrowUp className="h-5 w-5" />
                    </button>
                    <span className="my-1 font-medium">{upvotes[thread.id] || 0}</span>
                    <button
                      className="hover:text-[#F26430] transition-colors"
                      onClick={() => handleDownvote(thread.id)}
                    >
                      <ArrowDown className="h-5 w-5" />
                    </button>
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <CardHeader className="pb-2">
                      <div className="flex flex-wrap items-center gap-2 mb-1">
                        {thread.pinned && (
                          <Badge
                            variant="outline"
                            className="text-xs font-normal border-[#2a3a4a] text-[#F2C1AE]/70 px-1.5 py-0"
                          >
                            Pinned
                          </Badge>
                        )}
                      </div>

                      <Link
                        href={`/thread/${thread.id}`}
                        className="text-lg font-medium hover:underline line-clamp-1 text-white"
                      >
                        {thread.title}
                      </Link>

                      <div className="flex items-center gap-2 mt-1 text-sm text-[#F2C1AE]/70">
                        <Avatar className="h-5 w-5 border border-[#2a3a4a]">
                          <AvatarImage src={thread.authorAvatar} alt={thread.author} />
                          <AvatarFallback className="text-xs bg-[#F26430] text-white">
                            {thread.author[0].toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span>{thread.author}</span>
                        <span>•</span>
                        <span>{thread.date}</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-[#F2C1AE] line-clamp-2">{thread.excerpt}</p>
                      <div className="flex items-center mt-4 text-sm text-[#F2C1AE]/50">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        <span>{thread.replies} comments</span>
                      </div>
                    </CardContent>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="flex justify-center mt-8">
            <Button
              variant="outline"
              className="border-[#2a3a4a] text-[#F2C1AE]/70 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
            >
              Load More
            </Button>
          </div>
        </main>

        {/* Mobile new post button */}
        <div className="md:hidden fixed bottom-4 right-4">
          <Button className="h-12 w-12 rounded-full bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white shadow-lg">
            <Plus className="h-6 w-6" />
          </Button>
        </div>
        {/* Add the Footer component at the end of the main return statement, right before the closing </div> */}
        <Footer />
      </div>
    </div>
  )
}
